import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Check,
  Clear,
  Close,
  Download,
  FilterList,
} from "@mui/icons-material";
import TableComponent from "./../TableComponent";
import httpclient from "../../../Utils";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Autocomplete,
} from "@mui/material";
// import ViewOrderDialog from "../ViewOrderDialog";
import MuiAlert from "@mui/material/Alert";
// import StatusDialog from "../StatusDialog";
// import BackdropLoader from "../../../../Components/BackdropLoader";
import { useLocation, useNavigate } from "react-router";
import ViewProductDetail from "./ViewProductDetail";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";


//import { useLocation } from "react-router-dom";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const columns = [
  //{ id: "checkColumn", name: " " },
  { id: "productID", name: "ERPLY Product ID" },
  { id: "code", name: "Product Code" },
  { id: "name", name: "Product Name" },
  { id: "type", name: "Product Type" },
  { id: "categoryName", name: "Category" },
  { id: "groupName", name: "Group" },
  { id: "priceWithVat", name: "Price(with VAT)" },
  { id: "status", name: "Status" },
  { id: "lastModified", name: "Last Modified Date" },
  // { id: "active", name: "Active" },
];

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const ErplyProducts = (props) => {

  // console.log(props)
  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();
  const isMappingEnabled=props?.module_features && props?.module_features[0].is_active;


  const location = useLocation();
  const navigate = useNavigate();
  const buttonRef = useRef(null);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewDetails, setViewDetails] = useState({});
  const [openStatusDialog, setOpenStatusDialog] = useState(false);
  const [customStatus, setCustomStatus] = useState([]);
  const [exceptionStatus, setExceptionStatus] = useState([]);
  const [rows, setRows] = useState([]);
  const [exportRows, setExportRows] = useState("");
  const [rowChecked, setRowChecked] = useState([]);
  const [company, setCompany] = useState([]);
  const [selectedChecked, setSelectedChecked] = useState([]);
  const [orderChecked, setOrderChecked] = useState([]);
  const [status, setStatus] = useState("");
  const [selected, setSelected] = useState([]);
  const [selected1, setSelected1] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [backdropLoader, setBackdropLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [singleLoading, setSingleLoading] = useState(false);
  const [direction, setDirection] = useState(false);
  const [currentColumn, setCurrentColumn] = useState("");
  const [page, setPage] = useState(1);
  const [from, setFrom] = useState(1);
  const [to, setTo] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );

  const [rowsPerPage, setRowsPerPage] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );
  const [total, setTotal] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);

  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [companyList, setCompanyList] = useState([]);
  const [filterData, setFilterData] = useState({

    code: "",
    productID: "",
    status: "",
    type: "",
    groupName: "",
    categoryName: "",
    remove: false,
  });

  const [submittedData, setSubmittedData] = useState({

    code: "",
    productID: "",
    status: "",
    type: "",
    groupName: "",
    categoryName: "",
    submit: false,
  });

  useEffect(() => {
    if (

      filterData.code === "" ||
      filterData.productID === "" ||
      filterData.status === "" ||
      filterData.type === "" ||
      filterData.groupName === "" ||
      filterData.categoryName === ""

    ) {
      setSubmittedData({
        ...submittedData,
        submit: false,
      });
    }

    if (filterData.code === " ") filterData.code = "";
    if (filterData.productID === " ") filterData.productID = "";
    if (filterData.status === " ") filterData.status = "";
    if (filterData.type === " ") filterData.type = "";
    if (filterData.groupName === " ") filterData.groupName = "";
    if (filterData.categoryName === " ") filterData.categoryName = "";

    filterData.remove === true && handleFilter();
  }, [filterData]);

  useEffect(() => {
    let currentpolicy = JSON.parse(localStorage.getItem("erply_product_filter"));
    currentpolicy !== null && setFilterData(currentpolicy);

    currentpolicy == null
      ? getProducts()
      :
      currentpolicy.code == "" &&
        currentpolicy.productID == "" &&
        currentpolicy.status == "" &&
        currentpolicy.type == "" &&
        currentpolicy.groupName == "" &&
        currentpolicy.categoryName == "" &&

        currentpolicy.removed == false
        ? getProducts()
        : console.log("erply product");
  }, []);

  useEffect(() => {
    if (location.state !== null) {
      if (location.state?.id) {
        filterData.productID = location.state.id
        setTimeout(() => {
          handleFilter();
          navigate("#", { replace: true });
          handleView(location.state.id);
        }, 1500);
      }
      if (location.state?.startDate) {
        filterData.startDate = location.state.startDate
        filterData.endDate = location.state.endDate
        setTimeout(() => {
          handleFilter();
          navigate("#", { replace: true });
        }, 1500);
      }
      setTimeout(() => {
        navigate("#", { replace: true });
      }, 1500);
    }
  }, [location.state]);


  const getProducts = () => {
    setLoading(true);
    httpclient
      .get(`${props.request_name}&pagination=${rowsPerPage}&page=${page}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setRows(data.data);
          setTotal(data.meta.total);
          setRowsPerPage(parseInt(data.meta.per_page));
          setPage(data.meta.current_page);
          setFrom(data.meta.from);
          setTo(data.meta.to);
          setLoading(false);
        } else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };


  const handleView = (row) => {
    setSingleLoading(true);
    setOpenViewDialog(true);
    httpclient
      .get(`${props.request_name}/${row.productID || row}`)
      .then(({ data }) => {
        if (data) {
          setViewDetails(data.data);
          setSingleLoading(false);
        }
        else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };

  const sendDetails = (callback) => {
    if (callback.open === false) {
      setOpenViewDialog(false);
      setViewDetails({});
    }
    if (callback.refetch === true) {
      handleView(callback.productID);
      setTimeout(() => {
        handleFilter();
      }, 1000);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "soldTo") {
      setFilterData({
        ...filterData,
        soldToName: value,
        remove: false,
      });
    }
    if (name === "customStatus") {
      setFilterData({
        ...filterData,
        customStatusName: value,
        remove: false,
      });
    }
    if (name === "exceptionStatus") {
      setFilterData({
        ...filterData,
        exceptionStatusName: value,
        remove: false,
      });
    }
  };

  const handleChangeSoldTo = (value) => {
    setFilterData({
      ...filterData,
      soldTo: value !== null ? value.id : "",
      soldToName: value !== null ? value.name : "",
      remove: false,
    });
  };

  const handleChangeCustom = (value) => {
    setFilterData({
      ...filterData,
      customStatus: value !== null ? value.id : "",
      customStatusName: value !== null ? value.name : "",
      remove: false,
    });
  };

  const handleChangeException = (value) => {
    setFilterData({
      ...filterData,
      exceptionStatus: value !== null ? value.id : "",
      exceptionStatusName: value !== null ? value.name : "",
      remove: false,
    });
  };

  const handleFilter = () => {
    setSubmittedData({
      ...submittedData,

      code: filterData.code,
      productID: filterData.productID,
      type: filterData.type,
      status: filterData.status,
      groupName: filterData.groupName,
      categoryName: filterData.categoryName,
      submit: true,
    });

    filterData.remove = true;

    localStorage.setItem("erply_product_filter", JSON.stringify(filterData));


    setLoading(true);
    if (
      filterData.code ||
      filterData.productID ||
      filterData.type ||
      filterData.status ||
      filterData.categoryName ||
      filterData.groupName
    ) {

      httpclient
        .get(
          `${props.request_name}&filters[productID][$eq]=${filterData.productID
          }&filters[code][$eq]=${filterData.code
          }&filters[status][$eq]=${filterData.status
          }&filters[type][$eq]=${filterData.type
          }&filters[categoryName][$contains]=${filterData.categoryName
          }&filters[groupName][$contains]=${filterData.groupName
          }&pagination=${rowsPerPage}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(data.meta.per_page);
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        });
    } else {
      getProducts();
    }
  };



  const hadleFilterOpen = () => {
    setFilterOpen((prev) => !prev);
  };

  const handleChangeFilter = (e) => {
    const { name, value } = e.target;
    setFilterData({
      ...filterData,
      [name]: value,
      remove: false,
    });
  };
  // console.log('filter data', filterData);

  const handleRemove = (data) => {
    setExportRows("");
    if (data === "soldTo") {
      filterData.soldToName = "";
      submittedData.soldToName = "";
    }
    if (data === "customStatus") {
      filterData.customStatusName = "";
      submittedData.customStatusName = "";
    }
    if (data === "exceptionStatus") {
      filterData.exceptionStatusName = "";
      submittedData.exceptionStatusName = "";
    }
    if (data === "startDate") {
      setFilterData({
        ...filterData,
        startDate: "",
        endDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else if (data === "despatchByStartDate") {
      setFilterData({
        ...filterData,
        despatchByStartDate: "",
        despatchByEndDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        despatchByStartDate: "",
        despatchByEndDate: "",
      });
    } else {
      setFilterData({
        ...filterData,
        [data]: "",
        remove: true,
      });

      setSubmittedData({
        ...submittedData,
        [data]: "",
      });
    }
  };



  // const handleRowCheck = (e, row) => {
  //   console.log("row", row);
  //   var checkIfNotSame = false;
  //   selectedChecked.map((check) => {
  //     if (
  //       row.reportType !== check.reportType ||
  //       row.orderType !== check.orderType
  //     ) {
  //       return (checkIfNotSame = true);
  //     } else {
  //       return (checkIfNotSame = false);
  //     }
  //   });

  //   if (checkIfNotSame === true) {
  //     setButtonLoader(true);
  //     setTimeout(() => {
  //       alert("Please select only one type of exception order");
  //       setButtonLoader(false);
  //     }, 500);
  //   } else {
  //     setButtonLoader(false);
  //     const { checked } = e.target;
  //     if (checked === true) {
  //       setRowChecked([...rowChecked, row.orderID]);
  //       setCompany([...company, row.company.erpAccountCustomerID]);
  //       setSelectedChecked([...selectedChecked, row]);
  //       setOrderChecked([
  //         ...orderChecked,
  //         !row.orderProduct.some(
  //           (product) =>
  //             product.reasonCode === "3" ||
  //             product.reasonCode === "4" ||
  //             product.reasonCode === "5" ||
  //             product.reasonCode === "12" ||
  //             product.reasonCode === ""
  //         ) || accept(row),
  //       ]);
  //     } else {
  //       let newData = rowChecked.filter((check) => {
  //         return check !== row.orderID;
  //       });

  //       let newArr = selectedChecked.filter((select) => {
  //         return select.orderID !== row.orderID;
  //       });

  //       let newOrder = orderChecked.filter((order, index) => {
  //         return index !== rowChecked.indexOf(row.orderID);
  //       });
  //       let newArr1 = company.filter((order, index) => {
  //         return index !== rowChecked.indexOf(row.orderID);
  //       });

  //       // Update the 'company' state with the unique companies
  //       setCompany(newArr1);
  //       setRowChecked(newData);
  //       setSelectedChecked(newArr);
  //       setOrderChecked(newOrder);
  //     }
  //   }
  // };




  const handleSort = (column) => {
    setDirection((prevDirection) => !prevDirection);
    setCurrentColumn(column);
    setLoading(true);
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&filters[productID][$eq]=${filterData.productID
          }&filters[code][$eq]=${filterData.code
          }&filters[status][$eq]=${filterData.status
          }&filters[type][$eq]=${filterData.type
          }&filters[categoryName][$contains]=${filterData.categoryName
          }&filters[groupName][$contains]=${filterData.groupName
          }&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })

      : httpclient
        .get(
          `${props.request_name}&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangePage = (e, page) => {
    setLoading(true);
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&filters[productID][$eq]=${filterData.productID
          }&filters[code][$eq]=${filterData.code
          }&filters[status][$eq]=${filterData.status
          }&filters[type][$eq]=${filterData.type
          }&filters[categoryName][$contains]=${filterData.categoryName
          }&filters[groupName][$contains]=${filterData.groupName
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setLoading(true);

    localStorage.setItem("configRowPerPage", event.target.value);

    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&filters[productID][$eq]=${filterData.productID
          }&filters[code][$eq]=${filterData.code
          }&filters[status][$eq]=${filterData.status
          }&filters[type][$eq]=${filterData.type
          }&filters[categoryName][$contains]=${filterData.categoryName
          }&filters[groupName][$contains]=${filterData.groupName
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${+event.target.value}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${+event.target.value}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setPage(data.meta.current_page);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };


  return (
    <div>
      <Grid container spacing={2}>
        <Grid item md={8} xs={12}>
          <Header>
            <h1>List Products (ERPLY)</h1>
          </Header>
        </Grid>
        <Grid
          item
          md={4}
          xs={12}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
            Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
          </Button>
        </Grid>

        {/* Filter */}
        <Grid item xs={12}>
          <Collapse in={filterOpen}>
            <Card>
              <Box p={4}>
                <Grid container spacing={2}>

                  <Grid item xs={12} md={4}>
                    <InputLabel>ERPLY Product ID</InputLabel>
                    <TextField
                      name="productID"
                      value={filterData.productID}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Product Code</InputLabel>
                    <TextField
                      name="code"
                      value={filterData.code}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Product Type</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.type}
                        name="status"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        <MenuItem value={"MATRIX"}>Matrix</MenuItem>
                        <MenuItem value={"PRODUCT"}>Product</MenuItem>

                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Product Category</InputLabel>
                    <TextField
                      name="categoryName"
                      value={filterData.categoryName}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Product Group</InputLabel>
                    <TextField
                      name="groupName"
                      value={filterData.groupName}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Status</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.status}
                        name="status"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        <MenuItem value={"ACTIVE"}>ACTIVE</MenuItem>
                        <MenuItem value={"ARCHIVED"}>ARCHIVED</MenuItem>

                      </Select>
                    </FormControl>

                  </Grid>


                  <Grid item xs={12}>
                    <Box textAlign={"right"}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFilter}
                      >
                        Filter{" "}
                        <ArrowForward
                          fontSize="small"
                          style={{ marginLeft: "5px" }}
                        />
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          </Collapse>
        </Grid>

        {
          submittedData.code ||
            submittedData.productID ||
            submittedData.status ||
            submittedData.type ||
            submittedData.categoryName ||
            submittedData.groupName
            ? (
              <Grid item xs={12}>
                <FilteredBox>
                  <span>Filtered: </span>


                  {submittedData.code && (
                    <p>
                      <span>Product Code: {submittedData.code}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("code")}
                      />
                    </p>
                  )}
                  {submittedData.productID && (
                    <p>
                      <span>ERPLY Product ID: {submittedData.productID}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("productID")}
                      />
                    </p>
                  )}
                  {submittedData.type && (
                    <p>
                      <span>Product Type: {submittedData.type}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("type")}
                      />
                    </p>
                  )}
                  {submittedData.categoryName && (
                    <p>
                      <span>Product Category: {submittedData.categoryName}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("categoryName")}
                      />
                    </p>
                  )}
                  {submittedData.groupName && (
                    <p>
                      <span>Product Group: {submittedData.groupName}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("groupName")}
                      />
                    </p>
                  )}
                  {submittedData.status && (
                    <p>
                      <span>Status: {submittedData.status}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("status")}
                      />
                    </p>
                  )}


                </FilteredBox>
              </Grid>
            ) : (
              <Box></Box>
            )}
        {/* Filter */}



        <Grid item xs={12}>
          <TableComponent
            columns={columns}
            rows={rows}
            sort={true}
            handleView={handleView}
            handleSort={handleSort}
            loading={loading}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            handleChangePage={handleChangePage}
            // handleRowCheck={handleRowCheck}
            rowChecked={rowChecked}
            buttonLoader={buttonLoader}
            direction={direction}
            currentColumn={currentColumn}
            page={page}
            total={total && total}
            fromTable={from}
            toTable={to}
            rowsPerPage={rowsPerPage}
            filterData={filterData}
          />
        </Grid>
        <Footer overlay={overlay || props.overlayNew} />
      </Grid>

      {openViewDialog && (
        <ViewProductDetail
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
          isMappingEnabled={isMappingEnabled}
        />
      )}


      {/* {openStatusDialog && (
        <StatusDialog
          sendChangeOrder={sendChangeOrder}
          status={status}
          selectedChecked={selectedChecked}
          viewDetails={viewDetails}
        />
      )} */}

      {/* {backdropLoader && <BackdropLoader />} */}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ErplyProducts;
