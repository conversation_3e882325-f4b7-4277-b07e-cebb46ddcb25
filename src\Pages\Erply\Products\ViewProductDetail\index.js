import { Check, Clear, Close, Download, KeyboardArrowLeft, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar,
    TextField,
    FormControlLabel,
    Radio,
    RadioGroup
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";
import httpclient from "../../../../Utils";
import debounce from "lodash.debounce";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    // console.log(props);
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const variantsColumns = [
    //{ id: "checkColumn", name: " " },
    { id: "productID", name: "ERPLY Product ID" },
    { id: "code", name: "Product Code" },
    { id: "name", name: "Product Name" },
    { id: "type", name: "Product Type" },
    { id: "brandName", name: "Brand" },
    { id: "categoryName", name: "Category" },
    { id: "groupName", name: "Group" },
    { id: "priceWithVat", name: "Price(with VAT)" },
    { id: "status", name: "Status" },
    { id: "actions", name: "Actions" }
];

const sohColumns = [
    //{ id: "checkColumn", name: " " },
    { id: "erplyWarehouseID", name: "Warehouse ID" },
    { id: "warehouse_details", name: "Warehouse Name" },
    { id: "reservedStock", name: "Reserved Stocks" },
    { id: "totalStock", name: "Total Stocks" },
    { id: "erplyCurrentStockValue", name: "Available Stocks" },
];



const ViewProductDetail = (props) => {
    console.log(props)
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);
    const [sohDetails, setSohDetails] = useState("");
    const [mappingData, setMappingData] = useState({});
    const [mappingDetails, setMappingDetails] = useState("");

    const [showSearchUI, setShowSearchUI] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [variantSearchQuery, setVariantSearchQuery] = useState("");
    const [searchResults, setSearchResults] = useState([]);
    const [variantSearchResults, setVariantSearchResults] = useState([]);
    const [selectedProductId, setSelectedProductId] = useState(null);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [variantConfirmOpen, setVariantConfirmOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [forceMapConfirmOpen, setForceMapConfirmOpen] = useState(false);
    const [forceMapMessage, setForceMapMessage] = useState("");
    // const productId = props.viewDetails.productID;

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);


    useEffect(() => {
        if (props.viewDetails?.productID) {
            getShopifyMappingData(props.viewDetails.productID);
        }
    }, [props.viewDetails?.productID]);

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }

    const handleView = (row) => {
        setSohDetails(row);
    };

    const handleBack = () => {
        setSohDetails("");
    }

    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const getShopifyMappingData = async (productId) => {
        try {
            const response = await httpclient.get(`request-response?requestName=erply/v2/products/${productId}&serviceType=shopify`);
            if (response.data) {
                console.log(response.data.data);
                setMappingData(response.data.data);
            }
        } catch (err) {
            console.log(err);
        }
    }

    const performSearch = async (query) => {
        try {
            setLoading(true);
            const [titleRes, handleRes] = await Promise.all([
                httpclient.get(
                    `/request-response?requestName=shopify/v2/products&filters[title][$contains]=${query}`
                ),
                httpclient.get(
                    `/request-response?requestName=shopify/v2/products&filters[handle][$contains]=${query}`
                ),
            ]);

            const titleResults = titleRes.data.data || [];
            const handleResults = handleRes.data.data || [];

            const combinedResults = [
                ...titleResults,
                ...handleResults.filter(
                    (item) => !titleResults.some((titleItem) => titleItem.id === item.id)
                ),
            ];

            setSearchResults(combinedResults);
        } catch (error) {
            console.error("Search failed:", error);
            setSearchResults([]);
        } finally {
            setLoading(false);
        }
    };

    // Debounce search
    const debouncedSearch = debounce((query) => {
        if (query.trim()) performSearch(query);
        else setSearchResults([]);
    }, 500);

    useEffect(() => {
        debouncedSearch(searchQuery);
        return debouncedSearch.cancel;
    }, [searchQuery]);

    const performVariantSearch = async (query) => {
        try {
            setLoading(true);
            const [titleRes, handleRes, barcodeRes] = await Promise.all([
                httpclient.get(
                    `request-response?requestName=shopify/v2/products&productType=Variation&filters[displayName][$contains]=${query}`
                ),
                httpclient.get(
                    `request-response?requestName=shopify/v2/products&productType=Variation&filters[sku][$contains]=${query}`
                ),
                httpclient.get(
                    `request-response?requestName=shopify/v2/products&productType=Variation&filters[barcode][$contains]=${query}`
                ),
            ]);

            const titleResults = titleRes.data.data || [];
            const handleResults = handleRes.data.data || [];
            const barcodeResults = barcodeRes.data.data || [];

            const combinedResults = [
                ...titleResults,
                ...handleResults.filter(
                    (item) => !titleResults.some((titleItem) => titleItem.displayName === item.displayName)
                ),
                ...barcodeResults.filter(
                    (item) => !titleResults.some((titleItem) => titleItem.displayName === item.displayName) &&
                        !handleResults.some((handleItem) => handleItem.displayName === item.displayName)
                ),
            ];

            setVariantSearchResults(combinedResults);
        } catch (error) {
            console.error("Variant search failed:", error);
            setVariantSearchResults([]);
        } finally {
            setLoading(false);
        }
    };

    // Debounce variant search
    const debouncedVariantSearch = debounce((query) => {
        if (query.trim()) performVariantSearch(query);
        else setVariantSearchResults([]);
    }, 500);

    useEffect(() => {
        debouncedVariantSearch(variantSearchQuery);
        return debouncedVariantSearch.cancel;
    }, [variantSearchQuery]);

    const handleMatrixMap = async () => {
        console.log(selectedProductId);
        try {
            const res = await httpclient.post(`/request-response?requestName=erply/v2/mapping/v1/product`, {
                erplyProductID: props.viewDetails.productID,
                shopifyProductID: selectedProductId,
            });
            getShopifyMappingData(props.viewDetails.productID);
            setConfirmOpen(false);
            setShowSearchUI(false);
            setShowSearchUI(false);
        } catch (err) {
            console.error("Mapping failed", err);
        }
    };

    const getVariantMappingData = async (variantId) => {
        try {
            const res = await httpclient.get(`request-response?requestName=erply/v2/products/${variantId}&serviceType=shopify&productType=Variation`);
            // console.log(res.data.data);
            return res.data.data;
        } catch (err) {
            console.error("Failed to fetch variant mapping data:", err);
            return null;
        }
    }

    const handleMapping = async (row) => {
        const mappingData = await getVariantMappingData(row.productID);
        console.log(mappingData);
        setMappingDetails({
            ...row,
            mappingData: mappingData
        });
    };

    const handleBackFromMapping = () => {
        setMappingDetails("");
        setVariantSearchQuery("")
        setVariantSearchResults([])
    };

    const handleVariantMap = async () => {
        console.log("Mapping variant");
        console.log(mappingDetails.productID);
        console.log(selectedProductId);
        try {
            const res = await httpclient.post(`/request-response?requestName=erply/v2/mapping/v1/product&productType=Variation`, {
                erplyProductID: mappingDetails.productID,
                shopifyProductID: selectedProductId,
            });

            // Refresh variant mapping data
            const updatedMappingData = await getVariantMappingData(mappingDetails.productID);
            setMappingDetails({
                ...mappingDetails,
                mappingData: updatedMappingData
            });

            setVariantConfirmOpen(false);
            setShowSearchUI(false);
        } catch (err) {
            console.error("Variant mapping failed", err);

            if (err.response?.status === 404 &&
                err.response?.data?.message === "Parent Product Mapping ID mismatch between Erply and Shopify. Would you like to proceed with forced mapping?") {
                setForceMapMessage(err.response.data.message);
                setVariantConfirmOpen(false);
                setForceMapConfirmOpen(true);
            }
        }
    };

    const handleForceVariantMap = async () => {
        console.log("Force mapping variant");
        console.log(mappingDetails.productID);
        console.log(selectedProductId);
        try {
            const res = await httpclient.post(`/request-response?requestName=erply/v2/mapping/v1/product&productType=Variation&isForceMapped=1`, {
                erplyProductID: mappingDetails.productID,
                shopifyProductID: selectedProductId,
            });

            // Refresh variant mapping data
            const updatedMappingData = await getVariantMappingData(mappingDetails.productID);
            setMappingDetails({
                ...mappingDetails,
                mappingData: updatedMappingData
            });

            setForceMapConfirmOpen(false);
            setShowSearchUI(false);
        } catch (err) {
            console.error("Force variant mapping failed", err);
        }
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Product Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.name || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Details" {...a11yProps(0)} />
                                <Tab label="Variants" {...a11yProps(1)} />

                                {props?.isMappingEnabled === 1 && <Tab label="Mapping" {...a11yProps(2)} />}
                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container spacing={2}>
                                    {/* Left Side */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>ERPLY Product ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.productID || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>External ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.externalID || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Name</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.name || "-"}
                                            </Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Code</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.code || "-"}</Values>
                                        </FlexContent>


                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Type</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.type || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Category</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.categoryName || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Group</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.groupName || "-"}</Values>
                                        </FlexContent>
                                    </Grid>

                                    {/* Left Side */}

                                    {/* Right Side */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Price(with VAT)</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                ${parseFloat(props.viewDetails.priceWithVat).toFixed(2)}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Pending Process</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.pendingProcess === 1 ? <Check color="primary" /> : <Clear color="error" />}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Display In WebShop</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.displayedInWebshop === 1 ? <Check color="primary" /> : <Clear color="error" />}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Status</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.status}
                                            </Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Created Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {moment(props.viewDetails.created_at).format(
                                                    "ddd, DD MMM YYYY, h:mm a"
                                                ) || "-"}
                                            </Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Last Modified Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {moment(props.viewDetails.lastModified).format(
                                                    "ddd, DD MMM YYYY, h:mm a"
                                                ) || "-"}
                                            </Values>
                                        </FlexContent>
                                    </Grid>
                                    {/* Right Side */}

                                    <Grid item xs={12}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Long Description</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {/* {props.viewDetails.shortDescription || "-"} */}
                                                {displayText(props.viewDetails.description)}
                                            </Values>
                                        </FlexContent>
                                    </Grid>

                                    <Grid item xs={12}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Sales Text</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {/* {props.viewDetails.longDescription || "-"} */}
                                                {displayText(props.viewDetails.longdesc)}
                                            </Values>
                                        </FlexContent>
                                    </Grid>
                                </Grid>
                            </Box>
                        </TabPanel>

                        {/* <TabPanel value={value} index={1} dir={theme.direction}>

                            <Box>
                                {props.viewDetails.shopify_product_images && props.viewDetails.shopify_product_images.length > 0 ? (
                                    <>
                                        <h3>Shopify Images</h3>
                                        <ImageDiv>
                                            {props.viewDetails.shopify_product_images?.map((imgs, index) => (
                                                <ImageCell key={index} onClick={() => handleImageTogglerLanding(index)}>
                                                    <img src={imgs.src} alt="landing_image" />
                                                    <Box textAlign="center">
                                                        {imgs.name
                                                            .split("/")
                                                            .pop()
                                                            .replaceAll(".jpg", "")
                                                            .replaceAll(".png", "")}
                                                    </Box>
                                                </ImageCell>
                                            ))}
                                            <FsLightbox
                                                toggler={togglerLanding}
                                                sources={props.viewDetails.shopify_product_images?.map(img => img.src)}
                                                sourceIndex={imgIndex1}
                                                type="image"
                                                types={[
                                                    ...new Array(props.viewDetails.shopify_product_images?.length).fill("image"),
                                                ]}
                                            />
                                        </ImageDiv>
                                    </>
                                ) : (
                                    <h4>Images not available</h4>
                                )}
                            </Box>

                        </TabPanel>*/}



                        <TabPanel value={value} index={1} dir={theme.direction}>
                            {sohDetails ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        <h3>SOH Details</h3>
                                        <Button onClick={handleBack}>
                                            <KeyboardArrowLeft fontSize="small" sx={{ marginRight: "5px" }} />
                                            <span>Back</span>
                                        </Button>
                                    </Box>
                                    <BasicTable
                                        columns={sohColumns}
                                        rows={sohDetails.soh}
                                    />
                                </>
                            ) : mappingDetails ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        <h3>Variant Mapping Details</h3>
                                        <Button onClick={handleBackFromMapping}>
                                            <KeyboardArrowLeft fontSize="small" sx={{ marginRight: "5px" }} />
                                            <span>Back</span>
                                        </Button>
                                    </Box>

                                    {!mappingDetails.mappingData?.shopify_product ? (
                                        <>
                                            <Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
                                                No variant mapping found.
                                            </Typography>
                                            <Button variant="contained" color="primary" onClick={() => setShowSearchUI(true)}>
                                                Map Variant
                                            </Button>

                                            {showSearchUI && (
                                                <Box mt={2}>
                                                    <TextField
                                                        label="Search variant by Display Name or SKU or Barcode"
                                                        fullWidth
                                                        variant="outlined"
                                                        value={variantSearchQuery}
                                                        onChange={(e) => {
                                                            setVariantSearchQuery(e.target.value);
                                                            debouncedVariantSearch(e.target.value);
                                                        }}
                                                    />
                                                    {loading && <CircularProgress size={20} sx={{ mt: 2 }} />}

                                                    {!loading && variantSearchResults.length > 0 && (
                                                        <RadioGroup
                                                            value={selectedProductId}
                                                            onChange={(e) => {
                                                                const selected = variantSearchResults.find(
                                                                    (p) => p.shopifyVariantId === e.target.value
                                                                );
                                                                setSelectedProduct(selected);
                                                                setSelectedProductId(e.target.value);
                                                                setVariantConfirmOpen(true);
                                                            }}
                                                            sx={{ mt: 2 }}
                                                        >
                                                            {variantSearchResults.map((product) => (
                                                                <Box
                                                                    key={product.id}
                                                                    sx={{
                                                                        p: 2,
                                                                        border: "1px solid #ccc",
                                                                        borderRadius: "6px",
                                                                        mb: 1,
                                                                        backgroundColor:
                                                                            selectedProductId === product.shopifyVariantId ? "#f0f0f0" : "white",
                                                                        cursor: "pointer",
                                                                        "&:hover": {
                                                                            backgroundColor: "#f5f5f5",
                                                                        },
                                                                    }}
                                                                    onClick={() => {
                                                                        setSelectedProduct(product);
                                                                        setSelectedProductId(product.shopifyVariantId);
                                                                        setVariantConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <FormControlLabel
                                                                        value={product.shopifyVariantId}
                                                                        control={<Radio />}
                                                                        label={
                                                                            <Box>
                                                                                <Typography fontWeight="bold">{product.displayName}</Typography>
                                                                                <Typography variant="body2" color="textSecondary">
                                                                                    SKU: {product.sku} | Size: {product.size} | Color: {product.color}
                                                                                </Typography>
                                                                            </Box>
                                                                        }
                                                                    />
                                                                </Box>
                                                            ))}
                                                        </RadioGroup>
                                                    )}
                                                </Box>
                                            )}
                                        </>
                                    ) : (
                                        <>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} md={6}>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Display Name</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails?.mappingData.shopify_product?.displayName || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Shopify Parent ID</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails?.mappingData.shopify_product?.shopify_products_string_id || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Shopify Variant ID</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.shopifyVariantId || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Created At</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{moment(mappingDetails.mappingData.shopify_product?.created_at).format("ddd, DD MMM YYYY, h:mm a") || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Updated At</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{moment(mappingDetails.mappingData.shopify_product?.updated_at).format(
                                                            "ddd, DD MMM YYYY, h:mm a"
                                                        ) || "-"}</Values>
                                                    </FlexContent>
                                                </Grid>
                                                <Grid item xs={12} md={6}>

                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Price</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{Number(mappingDetails.mappingData.shopify_product?.price).toFixed(2) || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Size</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.size || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Color</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.color || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>SKU</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.sku || "-"}</Values>
                                                    </FlexContent>
                                                    <FlexContent>
                                                        <FlexInnerTitle>
                                                            <span>Bar Code</span> <span> : </span>
                                                        </FlexInnerTitle>
                                                        <Values>{mappingDetails.mappingData.shopify_product?.barcode || "-"}</Values>
                                                    </FlexContent>
                                                </Grid>
                                            </Grid>

                                            <Box mt={2}>
                                                <Button variant="outlined" onClick={() => setShowSearchUI(true)}>
                                                    Re-map Variant
                                                </Button>
                                            </Box>

                                            {showSearchUI && (
                                                <Box mt={2}>
                                                    <TextField
                                                        label="Search variant by Display Name or SKU or Barcode"
                                                        fullWidth
                                                        variant="outlined"
                                                        value={variantSearchQuery}
                                                        onChange={(e) => {
                                                            setVariantSearchQuery(e.target.value);
                                                            debouncedVariantSearch(e.target.value);
                                                        }}
                                                    />
                                                    {loading && <CircularProgress size={20} sx={{ mt: 2 }} />}

                                                    {!loading && variantSearchResults.length > 0 && (
                                                        <RadioGroup
                                                            value={selectedProductId}
                                                            onChange={(e) => {
                                                                const selected = variantSearchResults.find(
                                                                    (p) => p.shopifyVariantId === e.target.value
                                                                );
                                                                setSelectedProduct(selected);
                                                                setSelectedProductId(e.target.value);
                                                                setVariantConfirmOpen(true);
                                                            }}
                                                            sx={{ mt: 2 }}
                                                        >
                                                            {variantSearchResults.map((product) => (
                                                                <Box
                                                                    key={product.id}
                                                                    sx={{
                                                                        p: 2,
                                                                        border: "1px solid #ccc",
                                                                        borderRadius: "6px",
                                                                        mb: 1,
                                                                        backgroundColor:
                                                                            selectedProductId === product.shopifyVariantId ? "#f0f0f0" : "white",
                                                                        cursor: "pointer",
                                                                        "&:hover": {
                                                                            backgroundColor: "#f5f5f5",
                                                                        },
                                                                    }}
                                                                    onClick={() => {
                                                                        setSelectedProduct(product);
                                                                        setSelectedProductId(product.shopifyVariantId);
                                                                        setVariantConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <FormControlLabel
                                                                        value={product.shopifyVariantId}
                                                                        control={<Radio />}
                                                                        label={
                                                                            <Box>
                                                                                <Typography fontWeight="bold">{product.title}</Typography>
                                                                                <Typography variant="body2" color="textSecondary">
                                                                                    SKU: {product.sku} | Size: {product.size} | Color: {product.color}
                                                                                </Typography>
                                                                            </Box>
                                                                        }
                                                                    />
                                                                </Box>
                                                            ))}
                                                        </RadioGroup>
                                                    )}
                                                </Box>
                                            )}
                                        </>
                                    )}
                                </>
                            ) : (
                                <BasicTable
                                    columns={variantsColumns}
                                    rows={props.viewDetails.erply_product_variants}
                                    isMappingEnabled={props.isMappingEnabled}
                                    handleView={handleView}
                                    handleMapping={handleMapping}
                                />
                            )}
                        </TabPanel>

                        <TabPanel value={value} index={2} dir={theme.direction}>
                            <Box>
                                {!mappingData?.shopify_product ? (
                                    <>
                                        <Typography variant="h6" color="textSecondary">
                                            No product mapping found.
                                        </Typography>
                                        <Button variant="contained" color="primary" onClick={() => setShowSearchUI(true)}>
                                            Map Product
                                        </Button>
                                    </>
                                ) : (
                                    <>
                                        <Grid container spacing={2}>
                                            {/* Left Side */}
                                            <Grid item xs={12} md={6}>
                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Shopify Product ID</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{mappingData?.shopify_product?.id || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Product Name</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{mappingData?.shopify_product?.title || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Product Handle</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{mappingData?.shopify_product?.handle || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Product Shopify URL</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{mappingData?.shopify_product?.shopifyURL ? (
                                                        <a
                                                            href={mappingData.shopify_product.shopifyURL}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            style={{ color: "#3b82f6", textDecoration: "underline" }}
                                                        >
                                                            {mappingData.shopify_product.shopifyURL}
                                                        </a>
                                                    ) : (
                                                        "-"
                                                    )}</Values>
                                                </FlexContent>
                                            </Grid>

                                            {/* Right Side */}
                                            <Grid item xs={12} md={6}>
                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Status</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>{mappingData?.shopify_product?.status || "-"}</Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Shopify Added Date</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>
                                                        {mappingData?.shopify_product?.Shopify_added_date
                                                            ? moment(mappingData?.shopify_product?.Shopify_added_date).format(
                                                                "ddd, DD MMM YYYY, h:mm a"
                                                            )
                                                            : "-"}
                                                    </Values>
                                                </FlexContent>

                                                <FlexContent>
                                                    <FlexInnerTitle>
                                                        <span>Shopify Updated Date</span> <span> : </span>
                                                    </FlexInnerTitle>
                                                    <Values>
                                                        {mappingData?.shopify_product?.Shopify_updated_date
                                                            ? moment(mappingData?.shopify_product?.Shopify_updated_date).format(
                                                                "ddd, DD MMM YYYY, h:mm a"
                                                            )
                                                            : "-"}
                                                    </Values>
                                                </FlexContent>
                                            </Grid>
                                        </Grid>

                                        <Box mt={2}>
                                            <Button variant="outlined" onClick={() => setShowSearchUI(true)}>
                                                Re-map Product
                                            </Button>
                                        </Box>
                                    </>
                                )}

                                {showSearchUI && (
                                    <Box mt={2}>
                                        <TextField
                                            label="Search product by Title or Handle"
                                            fullWidth
                                            variant="outlined"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                        />
                                        {loading && <CircularProgress size={20} sx={{ mt: 2 }} />}

                                        {!loading && searchResults.length > 0 && (
                                            <RadioGroup
                                                value={selectedProductId}
                                                onChange={(e) => {
                                                    const selected = searchResults.find(
                                                        (p) => p.shopify_products_string_id === e.target.value
                                                    );
                                                    setSelectedProduct(selected);
                                                    setSelectedProductId(e.target.value);
                                                    setConfirmOpen(true);
                                                }}
                                                sx={{ mt: 2 }}
                                            >
                                                {searchResults.map((product) => (
                                                    <Box
                                                        key={product.id}
                                                        sx={{
                                                            p: 2,
                                                            border: "1px solid #ccc",
                                                            borderRadius: "6px",
                                                            mb: 1,
                                                            backgroundColor:
                                                                selectedProductId == product.id ? "#f0f0f0" : "white",
                                                        }}
                                                    >
                                                        <FormControlLabel
                                                            value={product.shopify_products_string_id}
                                                            control={<Radio />}
                                                            label={
                                                                <Box>
                                                                    <Typography fontWeight="bold">{product.title}</Typography>
                                                                    <Typography variant="body2" color="textSecondary">
                                                                        ID: {product.shopify_products_string_id} | Handle: {product.handle}
                                                                    </Typography>
                                                                </Box>
                                                            }
                                                        />
                                                    </Box>
                                                ))}
                                            </RadioGroup>
                                        )}
                                    </Box>
                                )}

                            </Box>
                        </TabPanel>

                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

            {/* {openPolicyDialog && (
        <ViewPolicyDialog
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )} */}

            {/* Product Mapping Confirmation Dialog */}
            <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
                <DialogTitle>Confirm Product Mapping</DialogTitle>
                <DialogContent>
                    <Typography>
                        Are you sure you want to map this product?
                    </Typography>
                    <Typography mt={1} fontWeight="bold">
                        {selectedProduct?.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                        ID: {selectedProduct?.shopify_products_string_id} | Handle: {selectedProduct?.handle}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
                    <Button variant="contained" onClick={handleMatrixMap}>
                        Confirm
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Variant Mapping Confirmation Dialog */}
            <Dialog open={variantConfirmOpen} onClose={() => setVariantConfirmOpen(false)}>
                <DialogTitle>Confirm Variant Mapping</DialogTitle>
                <DialogContent>
                    <Typography>
                        Are you sure you want to map this variant?
                    </Typography>
                    <Typography mt={1} fontWeight="bold">
                        {selectedProduct?.displayName}
                    </Typography>
                    <Typography variant="body2" mt={3}>
                        <span style={{ fontWeight: "bold" }}>SKU:</span> {selectedProduct?.sku}
                    </Typography>
                    <Typography variant="body2" mt={1}>
                        <span style={{ fontWeight: "bold" }}>Size:</span> {selectedProduct?.size}
                    </Typography>
                    <Typography variant="body2" mt={1}>
                        <span style={{ fontWeight: "bold" }}>Color:</span> {selectedProduct?.color}
                    </Typography>
                    <Typography variant="body2" mt={1}>
                        <span style={{ fontWeight: "bold" }}>BarCode:</span> {selectedProduct?.barcode}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setVariantConfirmOpen(false)}>Cancel</Button>
                    <Button variant="contained" onClick={handleVariantMap}>
                        Confirm
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Force Map Confirmation Dialog */}
            <Dialog open={forceMapConfirmOpen} onClose={() => setForceMapConfirmOpen(false)}>
                <DialogTitle>Force Mapping Required</DialogTitle>
                <DialogContent>
                    <Typography>
                        {forceMapMessage}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setForceMapConfirmOpen(false)}>No</Button>
                    <Button variant="contained" onClick={handleForceVariantMap}>
                        Yes
                    </Button>
                </DialogActions>
            </Dialog>

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewProductDetail;
